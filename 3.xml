<?xml version="1.0" encoding="UTF-8"?>
<log>
<logentry
   revision="7055">
<author>tanxianliang</author>
<date>2025-08-11T06:29:08.182290Z</date>
<paths>
<path
   prop-mods="false"
   text-mods="true"
   kind="file"
   action="M">/trunk/Engine/KEnginePub/Private/dx12/KGFX_GraphiceDeviceDx12.cpp</path>
</paths>
<msg>DX12使用Agility SDK 616版本</msg>
</logentry>
<logentry
   revision="7056">
<author>xiatian1</author>
<date>2025-08-11T06:31:39.008891Z</date>
<paths>
<path
   action="M"
   prop-mods="false"
   text-mods="true"
   kind="file">/trunk/Editor/Application/QParticleSysEditor/KEParticleCoreApp/Src/KEPEApplication_Internal.h</path>
<path
   text-mods="true"
   kind="file"
   action="M"
   prop-mods="false">/trunk/Editor/Application/QParticleSysEditor/KEParticleCoreApp/Src/KMaterialWidget.cpp</path>
<path
   text-mods="true"
   kind="file"
   action="M"
   prop-mods="false">/trunk/Editor/Application/QParticleSysEditor/KEParticleCoreApp/Src/KPsEditorConfig.cpp</path>
<path
   prop-mods="false"
   text-mods="true"
   kind="file"
   action="M">/trunk/Editor/Application/QParticleSysEditor/KEParticleCoreApp/Src/KPsEditorConfig.h</path>
<path
   action="M"
   prop-mods="false"
   text-mods="true"
   kind="file">/trunk/Editor/Application/QParticleSysEditor/KProxyFor3DEngine_Console/Src/KG3DModuleProxy_MeshQuote.cpp</path>
<path
   action="M"
   prop-mods="false"
   text-mods="true"
   kind="file">/trunk/Editor/KG3DEngineDX11/KG3DEngineE/Internal/Component/KG3D_ParticleSystem/KE3D_ParticleMeshQuoteLauncher.cpp</path>
<path
   text-mods="true"
   kind="file"
   action="M"
   prop-mods="false">/trunk/Editor/KG3DEngineDX11/KG3DEngineE/Internal/Component/KG3D_ParticleSystem/KE3D_ParticleMeshQuoteLauncher.h</path>
<path
   prop-mods="false"
   text-mods="true"
   kind="file"
   action="M">/trunk/Editor/KG3DEngineDX11/KG3DEngineE/Internal/InternalPublish/Include/ParticleSystem/IKE3D_ParticleSystem.h</path>
</paths>
<msg>修改模型引用传入数据接口</msg>
</logentry>
<logentry
   revision="7057">
<author>peikai</author>
<date>2025-08-11T06:32:39.013627Z</date>
<paths>
<path
   prop-mods="false"
   text-mods="true"
   kind="file"
   action="M">/trunk/Editor/ImNodeGraphEditor/ImNodeGraphCore/ImNodeGraph/KEditor.cpp</path>
<path
   action="M"
   prop-mods="false"
   text-mods="true"
   kind="file">/trunk/Editor/ImNodeGraphEditor/ImNodeGraphCore/ImNodeGraph/Undo/KUndoHistory.cpp</path>
<path
   action="M"
   prop-mods="false"
   text-mods="true"
   kind="file">/trunk/Editor/ImNodeGraphEditor/ImNodeGraphCore/ImNodeGraph/Undo/KUndoHistory.h</path>
<path
   kind="file"
   action="M"
   prop-mods="false"
   text-mods="true">/trunk/Editor/ImNodeGraphEditor/ImNodeGraphCore/ImNodeGraph/Widgets/KGraphWidget.cpp</path>
<path
   text-mods="true"
   kind="file"
   action="M"
   prop-mods="false">/trunk/Editor/ImNodeGraphEditor/ImNodeGraphCore/ImNodeGraph/Widgets/KGraphWidget.h</path>
<path
   prop-mods="false"
   text-mods="true"
   kind="file"
   action="M">/trunk/Editor/ImNodeGraphEditor/ImNodeGraphCore/ImNodeGraph/Widgets/KUndoCommandWidget.cpp</path>
</paths>
<msg>【ImNodeGraphEditor】 bug fixed when imgui update </msg>
</logentry>
</log>
